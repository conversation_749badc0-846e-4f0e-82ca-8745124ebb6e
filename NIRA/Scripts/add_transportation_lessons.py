#!/usr/bin/env python3
"""
Add Transportation Split Lessons
- Local Transportation (buses, auto, metro within city)
- Travel (trains, flights, long distance)
"""

import requests
import json
import uuid
from datetime import datetime

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Learning Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

# Transportation split lessons
TRANSPORTATION_LESSONS = [
    {
        "title": "Local Transportation",
        "description": "Learn about city transportation, buses, auto-rickshaws, and getting around locally",
        "topic": "Local Transportation",
        "sequence_order": 28,
        "vocabulary": [
            {"word": "பேருந்து", "translation": "Bus", "example": "பேருந்தில் பயணம் செய்கிறேன்"},
            {"word": "ஆட்டோ", "translation": "Auto-rickshaw", "example": "ஆட்டோவில் போகிறேன்"},
            {"word": "மெட்ரோ", "translation": "Metro", "example": "மெட்ரோ வேகமாக செல்கிறது"},
            {"word": "பேருந்து நிறுத்தம்", "translation": "Bus stop", "example": "பேருந்து நிறுத்தத்தில் காத்திருக்கிறேன்"},
            {"word": "டிக்கெட்", "translation": "Ticket", "example": "டிக்கெட் வாங்க வேண்டும்"},
            {"word": "கட்டணம்", "translation": "Fare", "example": "கட்டணம் எவ்வளவு?"},
            {"word": "ஓட்டுநர்", "translation": "Driver", "example": "ஓட்டுநர் நல்லவர்"},
            {"word": "பயணி", "translation": "Passenger", "example": "பயணிகள் ஏறுகிறார்கள்"},
            {"word": "நிறுத்தம்", "translation": "Stop", "example": "அடுத்த நிறுத்தத்தில் இறங்குகிறேன்"},
            {"word": "போக்குவரத்து", "translation": "Traffic", "example": "போக்குவரத்து அதிகம்"},
            {"word": "சாலை", "translation": "Road", "example": "சாலை நெரிசலாக உள்ளது"},
            {"word": "சிக்னல்", "translation": "Traffic signal", "example": "சிக்னலில் நிறுத்துகிறேன்"},
            {"word": "பாலம்", "translation": "Bridge", "example": "பாலத்தை கடக்கிறோம்"},
            {"word": "சந்திப்பு", "translation": "Junction", "example": "சந்திப்பில் திரும்புகிறேன்"},
            {"word": "வேகம்", "translation": "Speed", "example": "மெதுவாக செல்லுங்கள்"},
            {"word": "பாதுகாப்பு", "translation": "Safety", "example": "பாதுகாப்பு முக்கியம்"},
            {"word": "ஹெல்மெட்", "translation": "Helmet", "example": "ஹெல்மெட் அணியுங்கள்"},
            {"word": "சீட் பெல்ட்", "translation": "Seat belt", "example": "சீட் பெல்ட் கட்டுங்கள்"},
            {"word": "நேரம்", "translation": "Time", "example": "எவ்வளவு நேரம் ஆகும்?"},
            {"word": "தூரம்", "translation": "Distance", "example": "தூரம் எவ்வளவு?"},
            {"word": "வழி", "translation": "Route/Way", "example": "எந்த வழியாக போகலாம்?"},
            {"word": "திசை", "translation": "Direction", "example": "சரியான திசை எது?"},
            {"word": "இடது", "translation": "Left", "example": "இடது பக்கம் திரும்புங்கள்"},
            {"word": "வலது", "translation": "Right", "example": "வலது பக்கம் செல்லுங்கள்"},
            {"word": "நேராக", "translation": "Straight", "example": "நேராக செல்லுங்கள்"}
        ]
    },
    {
        "title": "Travel and Long Distance",
        "description": "Learn about trains, flights, hotels, and long-distance travel vocabulary",
        "topic": "Travel",
        "sequence_order": 29,
        "vocabulary": [
            {"word": "பயணம்", "translation": "Travel/Journey", "example": "நீண்ட பயணம் செல்கிறேன்"},
            {"word": "ரயில்", "translation": "Train", "example": "ரயிலில் பயணம் செய்கிறேன்"},
            {"word": "விமானம்", "translation": "Airplane", "example": "விமானத்தில் பறக்கிறேன்"},
            {"word": "ரயில் நிலையம்", "translation": "Railway station", "example": "ரயில் நிலையம் பெரியது"},
            {"word": "விமான நிலையம்", "translation": "Airport", "example": "விமான நிலையம் தூரம்"},
            {"word": "ஹோட்டல்", "translation": "Hotel", "example": "ஹோட்டலில் தங்குகிறேன்"},
            {"word": "அறை", "translation": "Room", "example": "அறை வேண்டும்"},
            {"word": "முன்பதிவு", "translation": "Reservation", "example": "முன்பதிவு செய்துள்ளேன்"},
            {"word": "சாமான்", "translation": "Luggage", "example": "சாமான் தயார் செய்கிறேன்"},
            {"word": "பாஸ்போர்ட்", "translation": "Passport", "example": "பாஸ்போர்ட் எங்கே?"},
            {"word": "விசா", "translation": "Visa", "example": "விசா வேண்டும்"},
            {"word": "சுற்றுலா", "translation": "Tourism", "example": "சுற்றுலா செல்கிறேன்"},
            {"word": "கடற்கரை", "translation": "Beach", "example": "கடற்கரையில் விளையாடுகிறேன்"},
            {"word": "மலை", "translation": "Mountain", "example": "மலையில் ஏறுகிறேன்"},
            {"word": "கோயில்", "translation": "Temple", "example": "கோயிலுக்கு செல்கிறேன்"},
            {"word": "அருங்காட்சியகம்", "translation": "Museum", "example": "அருங்காட்சியகம் பார்க்கிறேன்"},
            {"word": "புகைப்படம்", "translation": "Photograph", "example": "புகைப்படம் எடுக்கிறேன்"},
            {"word": "வழிகாட்டி", "translation": "Guide", "example": "வழிகாட்டி உதவுகிறார்"},
            {"word": "வரைபடம்", "translation": "Map", "example": "வரைபடம் பார்க்கிறேன்"},
            {"word": "திட்டம்", "translation": "Plan", "example": "பயண திட்டம் தயார்"},
            {"word": "செலவு", "translation": "Expense", "example": "பயண செலவு எவ்வளவு?"},
            {"word": "நாணயம்", "translation": "Currency", "example": "வெளிநாட்டு நாணயம்"},
            {"word": "மொழிபெயர்ப்பு", "translation": "Translation", "example": "மொழிபெயர்ப்பு உதவி வேண்டும்"},
            {"word": "அவசரநிலை", "translation": "Emergency", "example": "அவசரநிலையில் என்ன செய்வது?"},
            {"word": "பாதுகாப்பு", "translation": "Security", "example": "பாதுகாப்பு சோதனை"}
        ]
    }
]

def create_lesson_content(lesson_config):
    """Create comprehensive lesson content"""
    
    # Generate conversations
    conversations = [
        {
            "title": f"Using {lesson_config['topic']}",
            "scenario": f"A conversation about {lesson_config['topic'].lower()}",
            "difficulty": "beginner",
            "exchanges": [
                {
                    "text": f"{lesson_config['vocabulary'][0]['word']} எங்கே கிடைக்கும்?",
                    "speaker": "Person A",
                    "translation": f"Where can I find {lesson_config['vocabulary'][0]['translation'].lower()}?",
                    "pronunciation": f"{lesson_config['vocabulary'][0]['word']} enge kidaikkum?",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_01.mp3"
                },
                {
                    "text": f"அங்கே {lesson_config['vocabulary'][0]['word']} உள்ளது",
                    "speaker": "Person B", 
                    "translation": f"There is {lesson_config['vocabulary'][0]['translation'].lower()} over there",
                    "pronunciation": f"ange {lesson_config['vocabulary'][0]['word']} ulladhu",
                    "audio_url": f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_conv_01_02.mp3"
                }
            ]
        }
    ]
    
    # Generate grammar points
    grammar_points = [
        {
            "rule": "Transportation Expressions",
            "explanation": f"Learn how to express {lesson_config['topic'].lower()} needs and directions",
            "examples": [
                f"{lesson_config['vocabulary'][0]['example']}",
                f"{lesson_config['vocabulary'][1]['example']}",
                f"{lesson_config['vocabulary'][2]['example']}"
            ],
            "examples_audio_urls": [
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_01.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_02.mp3",
                f"https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/a1/lesson_{lesson_config['sequence_order']}_grammar_01_03.mp3"
            ],
            "tips": f"Practice asking for directions and {lesson_config['topic'].lower()} information"
        }
    ]
    
    # Generate exercises
    exercises = [
        {
            "type": "multiple_choice",
            "question": f"What is the Tamil word for '{lesson_config['vocabulary'][0]['translation']}'?",
            "options": [
                lesson_config['vocabulary'][0]['word'],
                lesson_config['vocabulary'][1]['word'], 
                lesson_config['vocabulary'][2]['word'],
                lesson_config['vocabulary'][3]['word']
            ],
            "correctAnswer": 0,
            "explanation": f"{lesson_config['vocabulary'][0]['word']} means {lesson_config['vocabulary'][0]['translation']}",
            "points": 10
        }
    ]
    
    return {
        "path_id": TAMIL_A1_PATH_ID,
        "title": lesson_config["title"],
        "description": lesson_config["description"],
        "lesson_type": "vocabulary",
        "difficulty_level": 1,
        "estimated_duration": 25,
        "sequence_order": lesson_config["sequence_order"],
        "learning_objectives": [
            f"Learn {lesson_config['topic'].lower()} vocabulary",
            "Practice pronunciation with audio",
            "Understand basic sentence structure"
        ],
        "vocabulary_focus": [],
        "grammar_concepts": ["Basic sentence structure", "Transportation expressions"],
        "cultural_notes": f"Tamil {lesson_config['topic'].lower()} in daily life context",
        "prerequisite_lessons": [],
        "content_metadata": {
            "vocabulary": lesson_config["vocabulary"],
            "conversations": conversations,
            "grammar_points": grammar_points,
            "exercises": exercises
        },
        "is_active": True,
        "has_audio": True,
        "audio_metadata": {
            "total_files": len(lesson_config["vocabulary"]) * 2,
            "voice_id": "9BWtsMINqrJLrRacOk9x",
            "generated_at": datetime.now().isoformat()
        }
    }

def add_lesson_to_database(lesson_data):
    """Add lesson to Supabase database"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        # Create lesson record
        lesson_record = {
            "id": str(uuid.uuid4()),
            "path_id": lesson_data["path_id"],
            "title": lesson_data["title"],
            "description": lesson_data["description"],
            "lesson_type": lesson_data["lesson_type"],
            "difficulty_level": lesson_data["difficulty_level"],
            "estimated_duration": lesson_data["estimated_duration"],
            "sequence_order": lesson_data["sequence_order"],
            "learning_objectives": lesson_data["learning_objectives"],
            "vocabulary_focus": lesson_data["vocabulary_focus"],
            "grammar_concepts": lesson_data["grammar_concepts"],
            "cultural_notes": lesson_data["cultural_notes"],
            "prerequisite_lessons": lesson_data["prerequisite_lessons"],
            "content_metadata": lesson_data["content_metadata"],
            "is_active": lesson_data["is_active"],
            "has_audio": lesson_data["has_audio"],
            "audio_metadata": lesson_data["audio_metadata"]
        }
        
        response = requests.post(url, json=lesson_record, headers=headers)
        
        if response.status_code == 201:
            print(f"✅ Successfully added lesson: {lesson_data['title']}")
            return True
        else:
            print(f"❌ Failed to add lesson: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding lesson: {e}")
        return False

def main():
    """Add transportation split lessons"""
    print("🚌 Adding Transportation Split Lessons")
    print("=" * 50)
    
    success_count = 0
    
    for lesson_config in TRANSPORTATION_LESSONS:
        print(f"\n📚 Creating lesson: {lesson_config['title']}")
        
        # Create lesson content
        lesson_data = create_lesson_content(lesson_config)
        
        # Add to database
        if add_lesson_to_database(lesson_data):
            success_count += 1
        
        print(f"⏳ Waiting 2 seconds...")
        import time
        time.sleep(2)
    
    print(f"\n🎉 Added {success_count}/{len(TRANSPORTATION_LESSONS)} lessons successfully!")
    print("📱 Transportation lessons are now split and available in the NIRA app!")

if __name__ == "__main__":
    main()
