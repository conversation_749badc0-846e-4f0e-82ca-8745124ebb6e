#!/usr/bin/env python3
"""
Final cleanup to ensure exactly 30 unique A1 lessons
Remove "Days, Months, and Time" (keep "Days, Weeks, Months, and Time")
"""

import requests
import json

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Tamil A1 Learning Path ID
TAMIL_A1_PATH_ID = "6b427613-420f-4586-bce8-2773d722f0b4"

def get_current_lessons():
    """Get all current A1 Tamil lessons"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{TAMIL_A1_PATH_ID}&select=id,title,sequence_order"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            lessons = response.json()
            print(f"📚 Found {len(lessons)} current A1 lessons:")
            for lesson in sorted(lessons, key=lambda x: x.get('sequence_order', 0)):
                print(f"  {lesson.get('sequence_order', '?')}: {lesson['title']}")
            return lessons
        else:
            print(f"❌ Failed to fetch lessons: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Error fetching lessons: {e}")
        return []

def delete_lesson(lesson_id, title):
    """Delete a lesson from database"""
    try:
        url = f"{SUPABASE_URL}/rest/v1/lessons?id=eq.{lesson_id}"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }
        
        response = requests.delete(url, headers=headers)
        if response.status_code == 204:
            print(f"✅ Deleted: {title}")
            return True
        else:
            print(f"❌ Failed to delete {title}: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error deleting {title}: {e}")
        return False

def main():
    """Final cleanup for A1 Tamil lessons"""
    print("🧹 Final A1 Tamil Lessons Cleanup")
    print("=" * 50)
    
    # Step 1: Get current lessons
    current_lessons = get_current_lessons()
    
    # Step 2: Delete the old "Days, Months, and Time" lesson
    print(f"\n🗑️ Removing old 'Days, Months, and Time' lesson...")
    
    deleted_count = 0
    for lesson in current_lessons:
        if lesson['title'] == "Days, Months, and Time":
            if delete_lesson(lesson['id'], lesson['title']):
                deleted_count += 1
    
    if deleted_count > 0:
        print(f"✅ Deleted {deleted_count} old lesson")
    else:
        print("ℹ️ No old 'Days, Months, and Time' lesson found")
    
    # Step 3: Get final count
    print(f"\n📊 Getting final lesson count...")
    final_lessons = get_current_lessons()
    
    print(f"\n🎉 Final A1 Tamil Curriculum:")
    print(f"📚 Total lessons: {len(final_lessons)}")
    
    if len(final_lessons) == 30:
        print("✅ Perfect! Exactly 30 unique A1 lessons")
    else:
        print(f"⚠️ Expected 30 lessons, got {len(final_lessons)}")
    
    print(f"\n📱 A1 Tamil curriculum is now complete and organized!")

if __name__ == "__main__":
    main()
