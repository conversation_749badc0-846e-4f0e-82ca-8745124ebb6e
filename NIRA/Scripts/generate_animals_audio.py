#!/usr/bin/env python3
"""
Complete Tamil Audio Generation for Animals and Nature Lesson
This script generates audio for ALL Tamil content and uploads to Supabase
"""

import os
import requests
import time
import json
import re
from pathlib import Path

# ElevenLabs API Key - REAL KEY FROM REPO
ELEVENLABS_API_KEY = "***************************************************"

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Voice IDs for Tamil content (user preferred Frey<PERSON> and <PERSON><PERSON>)
VOICE_IDS = [
    "jsCqWAovK2LkecY7zXl4",  # Freya - user preferred
    "EXAVITQu4vr4xnSDxMaL"   # Elli (Bella) - user preferred
]

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def fetch_lesson_content():
    """Fetch the Animals and Nature lesson content from Supabase"""
    try:
        print("📥 Fetching lesson content from Supabase...")

        # Query Supabase for the lesson content
        query_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "apikey": SUPABASE_ANON_KEY,
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json"
        }

        params = {
            "id": f"eq.{LESSON_ID}",
            "select": "id,title,content_metadata"
        }

        response = requests.get(query_url, headers=headers, params=params)

        if response.status_code == 200:
            lessons = response.json()
            if lessons and len(lessons) > 0:
                lesson = lessons[0]
                print(f"✅ Fetched lesson: {lesson['title']}")
                return lesson['content_metadata']
            else:
                print("❌ No lesson found with that ID")
                return None
        else:
            print(f"❌ Failed to fetch lesson: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        print(f"❌ Error fetching lesson: {e}")
        return None

def extract_tamil_content(content_metadata):
    """Extract all Tamil text from the lesson content"""
    tamil_items = []

    # Extract vocabulary
    vocabulary = content_metadata.get("vocabulary", [])
    for i, vocab in enumerate(vocabulary):
        word = vocab.get("word", "")
        example = vocab.get("example", "")

        if word:
            tamil_items.append({
                "text": word,
                "type": "vocabulary_word",
                "index": i,
                "filename": f"vocab_{i+1:02d}_word.mp3",
                "storage_path": f"tamil/a1/animals_nature/vocab_{i+1:02d}_word.mp3",
                "content_path": f"vocabulary.{i}.word_audio_url"
            })

        if example:
            tamil_items.append({
                "text": example,
                "type": "vocabulary_example",
                "index": i,
                "filename": f"vocab_{i+1:02d}_example.mp3",
                "storage_path": f"tamil/a1/animals_nature/vocab_{i+1:02d}_example.mp3",
                "content_path": f"vocabulary.{i}.example_audio_url"
            })

    # Extract conversations
    conversations = content_metadata.get("conversations", [])
    for conv_i, conversation in enumerate(conversations):
        exchanges = conversation.get("exchanges", [])
        for ex_i, exchange in enumerate(exchanges):
            text = exchange.get("text", "")
            if text and contains_tamil(text):
                tamil_items.append({
                    "text": text,
                    "type": "conversation",
                    "index": conv_i,
                    "sub_index": ex_i,
                    "filename": f"conv_{conv_i+1:02d}_{ex_i+1:02d}.mp3",
                    "storage_path": f"tamil/a1/animals_nature/conv_{conv_i+1:02d}_{ex_i+1:02d}.mp3",
                    "content_path": f"conversations.{conv_i}.exchanges.{ex_i}.audio_url"
                })

    # Extract grammar examples (Tamil parts only)
    grammar_points = content_metadata.get("grammar_points", [])
    for gram_i, grammar in enumerate(grammar_points):
        examples = grammar.get("examples", [])
        for ex_i, example in enumerate(examples):
            tamil_text = extract_tamil_from_example(example)
            if tamil_text:
                tamil_items.append({
                    "text": tamil_text,
                    "type": "grammar_example",
                    "index": gram_i,
                    "sub_index": ex_i,
                    "filename": f"grammar_{gram_i+1:02d}_{ex_i+1:02d}.mp3",
                    "storage_path": f"tamil/a1/animals_nature/grammar_{gram_i+1:02d}_{ex_i+1:02d}.mp3",
                    "content_path": f"grammar_points.{gram_i}.examples_audio_urls.{ex_i}"
                })

    # Extract exercise options (Tamil only)
    exercises = content_metadata.get("exercises", [])
    for ex_i, exercise in enumerate(exercises):
        options = exercise.get("options", [])
        for opt_i, option in enumerate(options):
            if contains_tamil(option):
                tamil_items.append({
                    "text": option,
                    "type": "exercise_option",
                    "index": ex_i,
                    "sub_index": opt_i,
                    "filename": f"exercise_{ex_i+1:02d}_option_{opt_i+1:02d}.mp3",
                    "storage_path": f"tamil/a1/animals_nature/exercise_{ex_i+1:02d}_option_{opt_i+1:02d}.mp3",
                    "content_path": f"exercises.{ex_i}.options_audio_urls.{opt_i}"
                })

    return tamil_items

def contains_tamil(text):
    """Check if text contains Tamil Unicode characters"""
    return any('\u0B80' <= char <= '\u0BFF' for char in text)

def extract_tamil_from_example(example):
    """Extract Tamil text from grammar examples"""
    # Format: "Tamil text (romanization) - English translation"
    if " - " in example:
        tamil_part = example.split(" - ")[0]
        # Remove romanization in parentheses
        tamil_only = re.sub(r'\s*\([^)]*\)', '', tamil_part)
        return tamil_only.strip()
    return ""

def generate_audio(text: str, output_file: str, voice_index: int = 0, max_retries: int = 3) -> bool:
    """Generate audio using ElevenLabs API with retry logic"""
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄 Retry {attempt}/{max_retries-1}: {text}")
            else:
                print(f"🎵 Generating: {text}")

            voice_id = VOICE_IDS[voice_index % len(VOICE_IDS)]
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"

            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": ELEVENLABS_API_KEY
            }

            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.8,
                    "style": 0.2,
                    "use_speaker_boost": True
                }
            }

            # Add timeout to prevent hanging
            response = requests.post(url, json=data, headers=headers, timeout=30)

            if response.status_code == 200:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(output_file), exist_ok=True)

                # Save audio file
                with open(output_file, 'wb') as f:
                    f.write(response.content)

                print(f"✅ Saved: {output_file}")
                return True
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(2)  # Wait before retry
                    continue
                return False

        except Exception as e:
            print(f"❌ Exception: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ Waiting 5 seconds before retry...")
                time.sleep(5)  # Longer wait for timeouts
                continue
            return False

    return False

def upload_to_supabase(file_path: str, storage_path: str) -> str:
    """Upload audio file to Supabase Storage and return public URL"""
    try:
        print(f"📤 Uploading {storage_path}...")

        # Read file
        with open(file_path, 'rb') as f:
            file_data = f.read()

        # Upload to Supabase Storage
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "audio/mpeg"
        }

        response = requests.post(upload_url, data=file_data, headers=headers)

        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Uploaded: {public_url}")
            return public_url
        elif response.status_code == 400 and "Duplicate" in response.text:
            # File already exists - return the public URL
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Already exists: {public_url}")
            return public_url
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return ""

    except Exception as e:
        print(f"❌ Upload error: {e}")
        return ""

def update_lesson_content(lesson_id: str, audio_urls: dict):
    """Update lesson content_metadata with audio URLs"""
    try:
        print(f"📝 Updating lesson content with {len(audio_urls)} audio URLs...")

        # This is a simplified update - in production you'd need to properly merge the URLs
        # into the existing content_metadata structure

        update_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "apikey": SUPABASE_ANON_KEY,
            "Prefer": "return=minimal"
        }

        # For now, just mark that the lesson has audio
        data = {
            "has_audio": True,
            "audio_metadata": {
                "generated_audio_count": len(audio_urls),
                "generation_date": time.strftime("%Y-%m-%d %H:%M:%S")
            }
        }

        response = requests.patch(
            f"{update_url}?id=eq.{lesson_id}",
            json=data,
            headers=headers
        )

        if response.status_code in [200, 204]:
            print(f"✅ Updated lesson metadata")
            return True
        else:
            print(f"❌ Update failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Update error: {e}")
        return False

def main():
    """Generate all Tamil audio for Animals and Nature lesson"""

    if ELEVENLABS_API_KEY == "YOUR_REAL_ELEVENLABS_API_KEY_HERE":
        print("❌ Please update ELEVENLABS_API_KEY with your real API key!")
        return

    print("🚀 Starting complete Tamil audio generation for Animals and Nature lesson")

    # 1. Fetch lesson content from Supabase
    content_metadata = fetch_lesson_content()
    if not content_metadata:
        print("❌ Failed to fetch lesson content")
        return

    # 2. Extract all Tamil content
    tamil_items = extract_tamil_content(content_metadata)
    print(f"📊 Found {len(tamil_items)} Tamil text items to generate audio for")

    if not tamil_items:
        print("❌ No Tamil content found in lesson")
        return

    # 3. Generate and upload audio for each Tamil text
    output_dir = "temp_audio"
    os.makedirs(output_dir, exist_ok=True)

    generated_count = 0
    audio_urls = {}

    # Load existing progress if available
    progress_file = "audio_generation_progress.json"
    if os.path.exists(progress_file):
        try:
            with open(progress_file, "r") as f:
                progress_data = json.load(f)
                audio_urls = progress_data.get("audio_urls", {})
                generated_count = progress_data.get("generated_count", 0)
                print(f"📋 Resuming from previous progress: {generated_count} files already generated")
        except:
            print("📋 Starting fresh generation")

    for i, item in enumerate(tamil_items):
        # Skip if already generated
        if item['content_path'] in audio_urls:
            print(f"⏭️  Skipping {i+1}/{len(tamil_items)}: {item['filename']} (already generated)")
            continue

        print(f"\n🎵 Processing {i+1}/{len(tamil_items)}: {item['text'][:50]}...")

        # Generate audio file
        local_file = f"{output_dir}/{item['filename']}"
        voice_index = i % len(VOICE_IDS)  # Alternate between Freya and Elli

        if generate_audio(item['text'], local_file, voice_index):
            # Upload to Supabase Storage
            public_url = upload_to_supabase(local_file, item['storage_path'])

            if public_url:
                audio_urls[item['content_path']] = public_url
                generated_count += 1
                print(f"✅ Complete: {item['filename']}")

                # Save progress every 10 files
                if generated_count % 10 == 0:
                    with open(progress_file, "w") as f:
                        json.dump({
                            "audio_urls": audio_urls,
                            "generated_count": generated_count
                        }, f, indent=2)
                    print(f"💾 Progress saved: {generated_count} files")

            # Clean up local file
            try:
                os.remove(local_file)
            except:
                pass

        # Rate limiting - increased for stability
        time.sleep(2)

    # 4. Update lesson in database
    if audio_urls:
        update_lesson_content(LESSON_ID, audio_urls)

    # 5. Save final progress and audio URLs
    with open(progress_file, "w") as f:
        json.dump({
            "audio_urls": audio_urls,
            "generated_count": generated_count,
            "completed": True
        }, f, indent=2)

    with open("generated_audio_urls.json", "w") as f:
        json.dump(audio_urls, f, indent=2)

    print(f"\n🎉 Tamil audio generation complete!")
    print(f"📊 Generated {generated_count}/{len(tamil_items)} audio files")
    print(f"📁 Audio URLs saved to: generated_audio_urls.json")
    print("\n📋 Next steps:")
    print("1. Manually update lesson content_metadata with specific audio URLs")
    print("2. Test audio playback in the app")
    print("3. Verify caching works correctly")

if __name__ == "__main__":
    main()
