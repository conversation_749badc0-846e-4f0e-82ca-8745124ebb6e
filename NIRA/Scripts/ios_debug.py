#!/usr/bin/env python3
"""
iOS Debug Script
Simulates exactly what the iOS app receives to find the null sequence_order issue
"""

import requests
import json

# Supabase configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

def debug_ios_api_calls():
    """Debug what iOS app receives"""
    headers = {
        'apikey': SUPABASE_KEY,
        'Authorization': f'Bearer {SUPABASE_KEY}',
        'Content-Type': 'application/json'
    }
    
    print("🔍 DEBUGGING iOS API CALLS")
    print("=" * 50)
    
    # 1. Check Tamil language
    print("\n1. Tamil Language Check:")
    response = requests.get(f"{SUPABASE_URL}/rest/v1/languages?name=eq.Tamil", headers=headers)
    if response.status_code == 200:
        tamil_lang = response.json()
        if tamil_lang:
            print(f"✅ Tamil language found: {tamil_lang[0]['id']}")
            tamil_id = tamil_lang[0]['id']
        else:
            print("❌ Tamil language not found")
            return
    else:
        print(f"❌ Error fetching Tamil language: {response.status_code}")
        return
    
    # 2. Check Tamil learning paths
    print("\n2. Tamil Learning Paths:")
    response = requests.get(f"{SUPABASE_URL}/rest/v1/learning_paths?language_id=eq.{tamil_id}", headers=headers)
    if response.status_code == 200:
        paths = response.json()
        print(f"✅ Found {len(paths)} Tamil paths:")
        for path in paths:
            print(f"  • {path.get('name', 'Unknown')} ({path.get('level', 'Unknown')}) - {path['id']}")
        
        # Find A1 path
        a1_paths = [p for p in paths if p.get('level') == 'A1']
        if a1_paths:
            main_a1_path = a1_paths[0]  # Use first A1 path
            print(f"\n📍 Using A1 path: {main_a1_path['name']} - {main_a1_path['id']}")
        else:
            print("❌ No A1 path found")
            return
    else:
        print(f"❌ Error fetching paths: {response.status_code}")
        return
    
    # 3. Check lessons in A1 path (what iOS app gets)
    print("\n3. Lessons in A1 Path (iOS App View):")
    path_id = main_a1_path['id']
    
    # This is likely what your iOS app calls
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{path_id}", headers=headers)
    if response.status_code == 200:
        lessons = response.json()
        print(f"✅ Found {len(lessons)} lessons")
        
        # Check each lesson for null sequence_order
        null_found = False
        for i, lesson in enumerate(lessons):
            seq = lesson.get('sequence_order')
            title = lesson.get('title', 'Unknown')
            
            if seq is None:
                print(f"❌ Index {i}: NULL sequence_order - {title}")
                null_found = True
            else:
                print(f"✅ Index {i}: seq={seq} - {title}")
            
            # Focus on index 2 where error occurs
            if i == 2:
                print(f"\n🎯 INDEX 2 DETAILED CHECK:")
                print(f"  • Title: {title}")
                print(f"  • Sequence Order: {seq}")
                print(f"  • Type: {type(seq)}")
                print(f"  • Is None: {seq is None}")
                print(f"  • Raw JSON: {json.dumps(lesson, indent=2)}")
                
                if seq is None:
                    print(f"  ❌ FOUND THE ISSUE: Index 2 has NULL sequence_order")
                    print(f"  📱 This is causing your iOS app to crash")
        
        if not null_found:
            print(f"\n✅ No null sequence_order values found")
            print(f"📱 The issue might be elsewhere...")
            
    else:
        print(f"❌ Error fetching lessons: {response.status_code}")
        return
    
    # 4. Try alternative API calls your iOS app might use
    print("\n4. Alternative API Calls:")
    
    # Ordered by sequence_order
    print("\n4a. Ordered by sequence_order:")
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{path_id}&order=sequence_order", headers=headers)
    if response.status_code == 200:
        lessons = response.json()
        print(f"✅ Found {len(lessons)} lessons (ordered)")
        for i, lesson in enumerate(lessons[:5]):
            seq = lesson.get('sequence_order')
            title = lesson.get('title', 'Unknown')
            status = "❌ NULL" if seq is None else f"✅ {seq}"
            print(f"  Index {i}: {status} - {title}")
    
    # With specific select fields
    print("\n4b. With specific fields:")
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons?path_id=eq.{path_id}&select=id,title,sequence_order&order=sequence_order", headers=headers)
    if response.status_code == 200:
        lessons = response.json()
        print(f"✅ Found {len(lessons)} lessons (specific fields)")
        for i, lesson in enumerate(lessons[:5]):
            seq = lesson.get('sequence_order')
            title = lesson.get('title', 'Unknown')
            status = "❌ NULL" if seq is None else f"✅ {seq}"
            print(f"  Index {i}: {status} - {title}")
    
    # 5. Check for any lessons with null sequence_order anywhere
    print("\n5. Global Null Check:")
    response = requests.get(f"{SUPABASE_URL}/rest/v1/lessons?sequence_order=is.null&select=id,title,path_id", headers=headers)
    if response.status_code == 200:
        null_lessons = response.json()
        if null_lessons:
            print(f"❌ Found {len(null_lessons)} lessons with null sequence_order:")
            for lesson in null_lessons:
                print(f"  • {lesson.get('title', 'Unknown')} (Path: {lesson.get('path_id', 'Unknown')})")
        else:
            print(f"✅ No lessons with null sequence_order found globally")
    
    print(f"\n📋 DEBUGGING SUMMARY:")
    print(f"  • If Index 2 shows NULL above, that's your iOS crash cause")
    print(f"  • If no NULLs found, the issue might be iOS app caching")
    print(f"  • Try force-closing and restarting your iOS app")
    print(f"  • Check if your iOS app uses a different API endpoint")

if __name__ == "__main__":
    debug_ios_api_calls()
