#!/usr/bin/env python3
"""
Retry Failed Audio Generation
Specifically retry the 3 files that failed due to timeout errors
"""

import requests
import os
import time
import json

# ElevenLabs API Key
ELEVENLABS_API_KEY = "***************************************************"

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Voice IDs for Frey<PERSON> and <PERSON>li
VOICE_IDS = [
    "jBpfuIE2acCO8z3wKNLl",  # Freya
    "jsCqWAovK2LkecY7zXl4"   # Elli
]

# Failed items to retry
FAILED_ITEMS = [
    {
        "text": "பூனை தூங்குகிறது",
        "filename": "grammar_05_01.mp3",
        "storage_path": "tamil/a1/animals_nature/grammar_05_01.mp3",
        "content_path": "grammar_points.4.examples_audio_urls.0",
        "voice_index": 0
    },
    {
        "text": "நாய் தூங்குகிறது", 
        "filename": "grammar_05_02.mp3",
        "storage_path": "tamil/a1/animals_nature/grammar_05_02.mp3",
        "content_path": "grammar_points.4.examples_audio_urls.1",
        "voice_index": 1
    },
    {
        "text": "பறவை தூங்குகிறது",
        "filename": "grammar_05_03.mp3", 
        "storage_path": "tamil/a1/animals_nature/grammar_05_03.mp3",
        "content_path": "grammar_points.4.examples_audio_urls.2",
        "voice_index": 0
    }
]

def generate_audio(text: str, output_file: str, voice_index: int = 0, max_retries: int = 5) -> bool:
    """Generate audio using ElevenLabs API with retry logic"""
    for attempt in range(max_retries):
        try:
            if attempt > 0:
                print(f"🔄 Retry {attempt}/{max_retries-1}: {text}")
            else:
                print(f"🎵 Generating: {text}")

            voice_id = VOICE_IDS[voice_index % len(VOICE_IDS)]
            url = f"https://api.elevenlabs.io/v1/text-to-speech/{voice_id}"
            
            headers = {
                "Accept": "audio/mpeg",
                "Content-Type": "application/json",
                "xi-api-key": ELEVENLABS_API_KEY
            }
            
            data = {
                "text": text,
                "model_id": "eleven_multilingual_v2",
                "voice_settings": {
                    "stability": 0.5,
                    "similarity_boost": 0.8,
                    "style": 0.2,
                    "use_speaker_boost": True
                }
            }
            
            # Add timeout to prevent hanging
            response = requests.post(url, json=data, headers=headers, timeout=45)
            
            if response.status_code == 200:
                # Create directory if it doesn't exist
                os.makedirs(os.path.dirname(output_file), exist_ok=True)
                
                # Save audio file
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ Saved: {output_file}")
                return True
            else:
                print(f"❌ Error {response.status_code}: {response.text}")
                if attempt < max_retries - 1:
                    time.sleep(3)  # Wait before retry
                    continue
                return False
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            if attempt < max_retries - 1:
                print(f"⏳ Waiting 10 seconds before retry...")
                time.sleep(10)  # Longer wait for timeouts
                continue
            return False
    
    return False

def upload_to_supabase(file_path: str, storage_path: str) -> str:
    """Upload audio file to Supabase Storage and return public URL"""
    try:
        print(f"📤 Uploading {storage_path}...")

        # Read file
        with open(file_path, 'rb') as f:
            file_data = f.read()

        # Upload to Supabase Storage
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "audio/mpeg"
        }
        
        response = requests.post(upload_url, data=file_data, headers=headers)
        
        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Uploaded: {public_url}")
            return public_url
        elif response.status_code == 400 and "Duplicate" in response.text:
            # File already exists - return the public URL
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Already exists: {public_url}")
            return public_url
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return ""
            
    except Exception as e:
        print(f"❌ Upload exception: {e}")
        return ""

def main():
    """Retry the 3 failed audio files"""
    
    print("🔄 Retrying 3 failed audio files...")
    
    output_dir = "temp_audio"
    os.makedirs(output_dir, exist_ok=True)
    
    audio_urls = {}
    success_count = 0
    
    for i, item in enumerate(FAILED_ITEMS):
        print(f"\n🎵 Processing {i+1}/3: {item['text']}...")
        
        # Generate audio file
        local_file = f"{output_dir}/{item['filename']}"
        
        if generate_audio(item['text'], local_file, item['voice_index']):
            # Upload to Supabase Storage
            public_url = upload_to_supabase(local_file, item['storage_path'])
            
            if public_url:
                audio_urls[item['content_path']] = public_url
                success_count += 1
                print(f"✅ Complete: {item['filename']}")
            
            # Clean up local file
            try:
                os.remove(local_file)
            except:
                pass
        
        # Rate limiting
        time.sleep(3)
    
    # Save results
    if audio_urls:
        with open("retry_audio_urls.json", "w") as f:
            json.dump(audio_urls, f, indent=2)
    
    print(f"\n🎉 Retry complete!")
    print(f"✅ Successfully generated: {success_count}/3 files")
    
    if success_count == 3:
        print("🎯 All failed files have been successfully generated!")
        print("📋 Next step: Merge these URLs with the main generated_audio_urls.json")
    else:
        print("⚠️  Some files still failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
