#!/usr/bin/env python3
"""
Upload Audio Files to Supabase and Update Database
This script uploads generated audio files and updates the lesson content with URLs
"""

import os
import requests
import json
from pathlib import Path

# Supabase Configuration
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ0NzI4NzQsImV4cCI6MjA1MDA0ODg3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8"

# Lesson ID for Animals and Nature
LESSON_ID = "b966c742-d36d-4d94-9e35-7c17a5039487"

def upload_file_to_supabase(file_path: str, storage_path: str) -> str:
    """Upload a file to Supabase Storage and return public URL"""
    try:
        print(f"📤 Uploading {file_path}...")
        
        # Read file
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        # Upload to Supabase Storage
        upload_url = f"{SUPABASE_URL}/storage/v1/object/lesson-audio/{storage_path}"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "audio/mpeg"
        }
        
        response = requests.post(upload_url, data=file_data, headers=headers)
        
        if response.status_code in [200, 201]:
            public_url = f"{SUPABASE_URL}/storage/v1/object/public/lesson-audio/{storage_path}"
            print(f"✅ Uploaded: {public_url}")
            return public_url
        else:
            print(f"❌ Upload failed: {response.status_code} - {response.text}")
            return ""
            
    except Exception as e:
        print(f"❌ Upload error: {e}")
        return ""

def update_lesson_content(lesson_id: str, updated_content: dict) -> bool:
    """Update lesson content_metadata in Supabase"""
    try:
        print(f"📝 Updating lesson {lesson_id}...")
        
        # Update lesson in database
        update_url = f"{SUPABASE_URL}/rest/v1/lessons"
        headers = {
            "Authorization": f"Bearer {SUPABASE_ANON_KEY}",
            "Content-Type": "application/json",
            "apikey": SUPABASE_ANON_KEY,
            "Prefer": "return=minimal"
        }
        
        data = {
            "content_metadata": updated_content,
            "has_audio": True
        }
        
        response = requests.patch(
            f"{update_url}?id=eq.{lesson_id}", 
            json=data, 
            headers=headers
        )
        
        if response.status_code in [200, 204]:
            print(f"✅ Updated lesson content")
            return True
        else:
            print(f"❌ Update failed: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Update error: {e}")
        return False

def process_audio_files():
    """Process all generated audio files and update lesson"""
    
    audio_dir = "Assets/Audio/Tamil/A1/Lesson24"
    
    if not os.path.exists(audio_dir):
        print(f"❌ Audio directory not found: {audio_dir}")
        print("Please run generate_animals_audio.py first!")
        return
    
    print("🚀 Starting audio upload and database update process")
    
    # Track uploaded URLs
    audio_urls = {
        "vocabulary": [],
        "conversations": [],
        "grammar": []
    }
    
    # 1. Upload vocabulary audio files
    print("\n📚 Uploading vocabulary audio...")
    for i in range(1, 26):  # 25 vocabulary items
        # Word audio
        word_file = f"{audio_dir}/vocab_{i:02d}_word.mp3"
        if os.path.exists(word_file):
            word_url = upload_file_to_supabase(
                word_file, 
                f"tamil/a1/lesson_24_vocab_{i:02d}_word.mp3"
            )
            if word_url:
                audio_urls["vocabulary"].append({
                    "type": "word",
                    "index": i,
                    "url": word_url
                })
        
        # Example audio
        example_file = f"{audio_dir}/vocab_{i:02d}_example.mp3"
        if os.path.exists(example_file):
            example_url = upload_file_to_supabase(
                example_file, 
                f"tamil/a1/lesson_24_vocab_{i:02d}_example.mp3"
            )
            if example_url:
                audio_urls["vocabulary"].append({
                    "type": "example", 
                    "index": i,
                    "url": example_url
                })
    
    # 2. Upload conversation audio files
    print("\n💬 Uploading conversation audio...")
    for i in range(1, 7):  # 6 conversation items
        conv_file = f"{audio_dir}/conv_{i:02d}.mp3"
        if os.path.exists(conv_file):
            conv_url = upload_file_to_supabase(
                conv_file, 
                f"tamil/a1/lesson_24_conv_{i:02d}.mp3"
            )
            if conv_url:
                audio_urls["conversations"].append({
                    "index": i,
                    "url": conv_url
                })
    
    # 3. Upload grammar audio files
    print("\n📖 Uploading grammar audio...")
    for i in range(1, 10):  # 9 grammar examples
        grammar_file = f"{audio_dir}/grammar_{i:02d}.mp3"
        if os.path.exists(grammar_file):
            grammar_url = upload_file_to_supabase(
                grammar_file, 
                f"tamil/a1/lesson_24_grammar_{i:02d}.mp3"
            )
            if grammar_url:
                audio_urls["grammar"].append({
                    "index": i,
                    "url": grammar_url
                })
    
    print(f"\n📊 Upload Summary:")
    print(f"   - Vocabulary: {len([u for u in audio_urls['vocabulary']])} files")
    print(f"   - Conversations: {len(audio_urls['conversations'])} files")
    print(f"   - Grammar: {len(audio_urls['grammar'])} files")
    
    # 4. Create updated lesson content structure
    print(f"\n📝 Preparing lesson content update...")
    
    # This is a simplified update - in practice, you'd fetch the current content
    # and merge in the audio URLs
    print(f"✅ Audio upload process completed!")
    print(f"\n📋 Next steps:")
    print(f"   1. Manually update lesson content_metadata with audio URLs")
    print(f"   2. Test audio playback in the app")
    print(f"   3. Repeat process for other lessons")
    
    # Save audio URLs to a file for reference
    with open("audio_urls.json", "w") as f:
        json.dump(audio_urls, f, indent=2)
    print(f"📁 Audio URLs saved to audio_urls.json")

def main():
    """Main function"""
    process_audio_files()

if __name__ == "__main__":
    main()
