#!/usr/bin/env python3
"""
Systematic Content Generator for NIRA Tamil Lessons A2-C2
Generates content following the new requirements:
- 20 vocabulary items per lesson
- 10 grammar points per lesson
- 15 conversations per lesson
- 15 practice exercises per lesson

This script processes lessons one by one to avoid timeouts and failures.
"""

import json
import requests
import time
from typing import Dict, List, Any
import google.generativeai as genai

# API Configuration
GEMINI_API_KEY = "AIzaSyAYasSocME9nPr7HP625EEx4cofwm5MH3Q"
SUPABASE_URL = "https://lyaojebttnqilmdosmjk.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5YW9qZWJ0dG5xaWxtZG9zbWprIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwMzQ4NjAsImV4cCI6MjA2MzYxMDg2MH0.F-JlumTRdQLg1h8ceUz9E71WwowTEzVN1w0I7hYX7_c"

# Initialize Gemini
genai.configure(api_key=GEMINI_API_KEY)
gemini_model = genai.GenerativeModel('gemini-2.0-flash-exp')

# Tamil Learning Path IDs
TAMIL_PATHS = {
    'A2': '0b14776f-f0b1-4e65-8fac-40a4ce8f125b', 
    'B1': '26aa02d3-7849-49ba-9da1-4ed61518d736',
    'B2': '6a5619db-bc89-4ab8-98e7-9f74cbcad793',
    'C1': 'ea76fb1b-04cd-4713-bd31-1b4c5a325ad8',
    'C2': '7b2f84d7-47ce-4e19-bb79-43926188fe4e'
}

class SystematicContentGenerator:
    """Systematic content generator for Tamil lessons"""
    
    def __init__(self):
        self.headers = {
            'apikey': SUPABASE_KEY,
            'Authorization': f'Bearer {SUPABASE_KEY}',
            'Content-Type': 'application/json'
        }
    
    def get_lessons_by_level(self, level: str) -> List[Dict[str, Any]]:
        """Get all lessons for a specific level"""
        
        path_id = TAMIL_PATHS[level]
        
        try:
            response = requests.get(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={
                    'path_id': f'eq.{path_id}',
                    'select': 'id,title,sequence_order,content_metadata',
                    'order': 'sequence_order'
                }
            )
            
            if response.status_code == 200:
                lessons = response.json()
                print(f"📚 Found {len(lessons)} {level} lessons")
                return lessons
            else:
                print(f"❌ Failed to fetch {level} lessons: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Error fetching lessons: {e}")
            return []
    
    def generate_vocabulary(self, lesson_title: str, level: str) -> List[Dict[str, Any]]:
        """Generate 20 vocabulary items for a lesson"""
        
        prompt = f"""
        Generate exactly 20 authentic Tamil vocabulary items for the {level} level lesson: "{lesson_title}"
        
        Requirements:
        - {level} difficulty level appropriate
        - Authentic Chennai Tamil dialect
        - Topic-specific to {lesson_title}
        - Include romanized pronunciation
        - Practical, real-world usage
        
        Return as valid JSON array with exactly 20 items:
        [
            {{
                "word": "Tamil word",
                "translation": "English translation",
                "pronunciation": "romanized pronunciation", 
                "example": "Tamil example sentence (romanized) - English translation",
                "difficulty": "{level.lower()}",
                "part_of_speech": "noun/verb/adjective/etc"
            }}
        ]
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text.strip()
            
            # Extract JSON from response
            if '```json' in content:
                content = content.split('```json')[1].split('```')[0]
            elif '```' in content:
                content = content.split('```')[1].split('```')[0]
            
            vocabulary = json.loads(content)
            
            if len(vocabulary) == 20:
                print(f"✅ Generated 20 vocabulary items for {lesson_title}")
                return vocabulary
            else:
                print(f"⚠️  Generated {len(vocabulary)} items, expected 20")
                return vocabulary[:20] if len(vocabulary) > 20 else vocabulary
                
        except Exception as e:
            print(f"❌ Failed to generate vocabulary: {e}")
            return []
    
    def generate_grammar(self, lesson_title: str, level: str) -> List[Dict[str, Any]]:
        """Generate 10 grammar points for a lesson"""
        
        prompt = f"""
        Generate exactly 10 Tamil grammar points for the {level} level lesson: "{lesson_title}"
        
        Requirements:
        - {level} difficulty level appropriate
        - Topic-related grammar concepts
        - Clear explanations in English
        - Tamil examples with romanization
        - Progressive complexity
        
        Return as valid JSON array with exactly 10 items:
        [
            {{
                "concept": "Grammar concept name",
                "explanation": "Clear explanation in English",
                "tamil_example": "Tamil example",
                "romanized_example": "Romanized pronunciation",
                "english_translation": "English translation",
                "usage_notes": "When and how to use this"
            }}
        ]
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text.strip()
            
            # Extract JSON from response
            if '```json' in content:
                content = content.split('```json')[1].split('```')[0]
            elif '```' in content:
                content = content.split('```')[1].split('```')[0]
            
            grammar = json.loads(content)
            
            if len(grammar) == 10:
                print(f"✅ Generated 10 grammar points for {lesson_title}")
                return grammar
            else:
                print(f"⚠️  Generated {len(grammar)} grammar points, expected 10")
                return grammar[:10] if len(grammar) > 10 else grammar
                
        except Exception as e:
            print(f"❌ Failed to generate grammar: {e}")
            return []
    
    def generate_conversations(self, lesson_title: str, level: str) -> List[Dict[str, Any]]:
        """Generate 15 conversations for a lesson"""
        
        prompt = f"""
        Generate exactly 15 Tamil conversations for the {level} level lesson: "{lesson_title}"
        
        Requirements:
        - {level} difficulty level appropriate
        - Topic-specific to {lesson_title}
        - Realistic, practical dialogues
        - 2-4 exchanges per conversation
        - Include romanized pronunciation
        - Authentic Chennai Tamil
        
        Return as valid JSON array with exactly 15 items:
        [
            {{
                "scenario": "Brief scenario description",
                "dialogue": [
                    {{
                        "speaker": "Person A",
                        "tamil": "Tamil text",
                        "romanized": "Romanized pronunciation",
                        "english": "English translation"
                    }},
                    {{
                        "speaker": "Person B", 
                        "tamil": "Tamil text",
                        "romanized": "Romanized pronunciation",
                        "english": "English translation"
                    }}
                ]
            }}
        ]
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text.strip()
            
            # Extract JSON from response
            if '```json' in content:
                content = content.split('```json')[1].split('```')[0]
            elif '```' in content:
                content = content.split('```')[1].split('```')[0]
            
            conversations = json.loads(content)
            
            if len(conversations) == 15:
                print(f"✅ Generated 15 conversations for {lesson_title}")
                return conversations
            else:
                print(f"⚠️  Generated {len(conversations)} conversations, expected 15")
                return conversations[:15] if len(conversations) > 15 else conversations
                
        except Exception as e:
            print(f"❌ Failed to generate conversations: {e}")
            return []
    
    def generate_exercises(self, lesson_title: str, level: str) -> List[Dict[str, Any]]:
        """Generate 15 practice exercises for a lesson"""
        
        prompt = f"""
        Generate exactly 15 Tamil practice exercises for the {level} level lesson: "{lesson_title}"
        
        Requirements:
        - {level} difficulty level appropriate
        - Mix of exercise types: multiple choice, fill-in-blank, matching, translation
        - Topic-specific to {lesson_title}
        - Include romanized pronunciations in options
        - Clear explanations
        
        Return as valid JSON array with exactly 15 items:
        [
            {{
                "type": "multiple_choice/fill_in_blank/matching/translation",
                "question": "Question text in English",
                "options": ["Option 1 (romanized)", "Option 2 (romanized)", "Option 3", "Option 4"],
                "correct_answer": 0,
                "explanation": "Explanation of correct answer",
                "points": 10
            }}
        ]
        """
        
        try:
            response = gemini_model.generate_content(prompt)
            content = response.text.strip()
            
            # Extract JSON from response
            if '```json' in content:
                content = content.split('```json')[1].split('```')[0]
            elif '```' in content:
                content = content.split('```')[1].split('```')[0]
            
            exercises = json.loads(content)
            
            if len(exercises) == 15:
                print(f"✅ Generated 15 exercises for {lesson_title}")
                return exercises
            else:
                print(f"⚠️  Generated {len(exercises)} exercises, expected 15")
                return exercises[:15] if len(exercises) > 15 else exercises
                
        except Exception as e:
            print(f"❌ Failed to generate exercises: {e}")
            return []
    
    def update_lesson_content(self, lesson_id: str, content_metadata: Dict[str, Any]) -> bool:
        """Update lesson with generated content"""
        
        try:
            response = requests.patch(
                f"{SUPABASE_URL}/rest/v1/lessons",
                headers=self.headers,
                params={'id': f'eq.{lesson_id}'},
                json={'content_metadata': content_metadata}
            )
            
            if response.status_code == 204:
                return True
            else:
                print(f"❌ Failed to update lesson: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Update error: {e}")
            return False
    
    def process_lesson(self, lesson: Dict[str, Any], level: str) -> bool:
        """Process a single lesson - generate all content"""
        
        lesson_id = lesson['id']
        lesson_title = lesson['title']
        
        print(f"\n🎯 Processing: {lesson_title}")
        print("-" * 50)
        
        # Generate all content types
        print("📚 Generating vocabulary...")
        vocabulary = self.generate_vocabulary(lesson_title, level)
        time.sleep(2)
        
        print("📖 Generating grammar...")
        grammar = self.generate_grammar(lesson_title, level)
        time.sleep(2)
        
        print("💬 Generating conversations...")
        conversations = self.generate_conversations(lesson_title, level)
        time.sleep(2)
        
        print("🧩 Generating exercises...")
        exercises = self.generate_exercises(lesson_title, level)
        time.sleep(2)
        
        # Validate content
        if not all([vocabulary, grammar, conversations, exercises]):
            print("❌ Failed to generate complete content")
            return False
        
        # Update lesson
        content_metadata = {
            'vocabulary': vocabulary,
            'grammar_points': grammar,
            'conversations': conversations,
            'exercises': exercises
        }
        
        if self.update_lesson_content(lesson_id, content_metadata):
            print(f"✅ Updated {lesson_title} with complete content")
            return True
        else:
            print(f"❌ Failed to update {lesson_title}")
            return False

def main():
    """Main function - Process lessons by level"""
    
    print("🚀 SYSTEMATIC CONTENT GENERATOR")
    print("Generating content for A2, B1, B2, C1, C2 lessons")
    print("=" * 60)
    
    generator = SystematicContentGenerator()
    
    # Process each level
    levels = ['A2', 'B1', 'B2', 'C1', 'C2']
    
    for level in levels:
        print(f"\n📚 PROCESSING {level} LEVEL")
        print("=" * 40)
        
        lessons = generator.get_lessons_by_level(level)
        
        if not lessons:
            print(f"⚠️  No lessons found for {level}")
            continue
        
        success_count = 0
        
        for i, lesson in enumerate(lessons, 1):
            print(f"\n🔄 {level} Lesson {i}/{len(lessons)}")
            
            if generator.process_lesson(lesson, level):
                success_count += 1
            
            # Prevent rate limiting
            time.sleep(3)
        
        print(f"\n📊 {level} RESULTS: {success_count}/{len(lessons)} lessons completed")
    
    print(f"\n🎉 CONTENT GENERATION COMPLETE!")
    print("📋 Next: Generate audio for all lessons")

if __name__ == "__main__":
    main()
