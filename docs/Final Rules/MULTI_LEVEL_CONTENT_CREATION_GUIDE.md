# 🚀 MULTI-<PERSON><PERSON>VEL CONTENT CREATION GUIDE

## 📋 **OVERVIEW**

This guide documents the systematic approach for creating A2, B1, B2, C1, C2 Tamil lessons following the proven A1 structure.

---

## 🗄️ **CURRENT A1 DATABASE STRUCTURE**

### **Tamil A1 Path Details**
- **Path ID**: `6b427613-420f-4586-bce8-2773d722f0b4`
- **Name**: "Tamil A1 Course"
- **Level**: "A1"
- **Total Lessons**: 30 (complete with audio)

### **Lessons Table Schema**
```sql
CREATE TABLE lessons (
    id UUID PRIMARY KEY,
    path_id UUID NOT NULL,
    title VARCHAR NOT NULL,
    description TEXT,
    lesson_type VARCHAR NOT NULL,
    difficulty_level INTEGER,
    estimated_duration INTEGER,
    sequence_order INTEGER,
    learning_objectives TEXT[],
    vocabulary_focus TEXT[],
    grammar_concepts TEXT[],
    cultural_notes TEXT,
    prerequisite_lessons TEXT[],
    content_metadata JSONB,
    is_active BOOLEAN,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    audio_url TEXT,
    audio_metadata JSONB,
    has_audio BOOLEAN
);
```

---

## 🎯 **NEW CONTENT REQUIREMENTS**

### **Content Counts (A2-C2)**
- **Vocabulary**: 20 items per lesson
- **Grammar**: 10 points per lesson  
- **Conversations**: 15 exchanges per lesson
- **Practice Exercises**: 15 exercises per lesson

### **Lesson Counts by Level**
- **A2**: 30 lessons
- **B1**: 20 lessons
- **B2**: 20 lessons
- **C1**: 15 lessons
- **C2**: 15 lessons

---

## 📁 **EXISTING SCRIPTS LOCATION**

### **Working Scripts Found**
1. **`Scripts/simple_multilevel_creator.py`** ✅
   - Creates A2, B1, B2, C1, C2 lessons
   - Uses existing Tamil A1 path ID
   - Generates appropriate vocabulary for each level

2. **`Scripts/comprehensive_lesson_generator.py`** ✅
   - Generates full content (vocab, grammar, conversations, exercises)
   - Uses Gemini + dual validation

3. **`Scripts/create_a2_lessons.py`** ✅
   - Specific A2 lesson creator
   - Follows A1 structure

4. **`Scripts/b1_vocabulary_validator.py`** ✅
   - B1 specific vocabulary generation

---

## 🔄 **SYSTEMATIC APPROACH**

### **Phase 1: Create Learning Paths**
Create separate learning paths for each level using existing Tamil paths as reference:

**Existing Tamil Paths:**
- A1: `6b427613-420f-4586-bce8-2773d722f0b4` ✅
- A2: `0b14776f-f0b1-4e65-8fac-40a4ce8f125b` ✅
- B1: `26aa02d3-7849-49ba-9da1-4ed61518d736` ✅
- B2: `6a5619db-bc89-4ab8-98e7-9f74cbcad793` ✅
- C1: `ea76fb1b-04cd-4713-bd31-1b4c5a325ad8` ✅
- C2: `7b2f84d7-47ce-4e19-bb79-43926188fe4e` ✅

### **Phase 2: Create Lesson Titles**
Generate lesson titles for each level following difficulty progression.

### **Phase 3: Create Lesson Structure**
Create basic lesson entries with proper metadata structure.

### **Phase 4: Generate Content**
Systematically generate content for each lesson:
1. Vocabulary (20 items)
2. Grammar (10 points)
3. Conversations (15 exchanges)
4. Practice Exercises (15 exercises)

### **Phase 5: Audio Generation**
Generate audio for all content using ElevenLabs.

---

## 🛠️ **IMPLEMENTATION PLAN**

### **Step 1: Verify Database Structure**
✅ **COMPLETED** - All Tamil learning paths exist

### **Step 2: Create Lesson Titles Script**
Create a script to generate appropriate lesson titles for each level.

### **Step 3: Create Systematic Content Generator**
Modify existing scripts to work with new content requirements.

### **Step 4: Process One Level at a Time**
- Start with A2 (30 lessons)
- Then B1 (20 lessons)
- Then B2 (20 lessons)
- Then C1 (15 lessons)
- Finally C2 (15 lessons)

---

## 📊 **QUALITY CHECKLIST**

### **Content Standards**
- [ ] Proper Tamil romanization
- [ ] No English placeholders
- [ ] Topic-specific content
- [ ] Progressive difficulty
- [ ] Cultural authenticity
- [ ] Audio URL structure

### **Database Standards**
- [ ] Correct path_id assignment
- [ ] Proper difficulty_level (2-6)
- [ ] Sequential sequence_order
- [ ] Complete content_metadata
- [ ] Audio metadata structure

---

## 🎵 **AUDIO STRUCTURE**

### **Base URL Pattern**
```
https://lyaojebttnqilmdosmjk.supabase.co/storage/v1/object/public/lesson-audio/tamil/{level}/{lesson_slug}/
```

### **Audio Files Per Lesson**
- **Vocabulary**: 40 files (20 words + 20 examples)
- **Conversations**: 30 files (15 conversations × 2 exchanges)
- **Grammar**: 20 files (10 points × 2 examples)
- **Exercises**: 60 files (15 exercises × 4 options)
- **Total**: ~150 files per lesson

---

## 🛠️ **CREATED SCRIPTS**

### **1. `Scripts/systematic_multilevel_creator.py`** ✅
**Purpose**: Creates lesson structures for all levels (A2-C2)
- Generates appropriate lesson titles for each level
- Creates basic lesson database entries
- Uses correct path IDs and difficulty levels
- Processes one level at a time to avoid failures

**Usage**:
```bash
cd /Users/<USER>/Documents/NIRA
python Scripts/systematic_multilevel_creator.py
```

### **2. `Scripts/systematic_content_generator.py`** ✅
**Purpose**: Generates content for existing lesson structures
- 20 vocabulary items per lesson
- 10 grammar points per lesson
- 15 conversations per lesson
- 15 practice exercises per lesson
- Processes lessons one by one to avoid timeouts

**Usage**:
```bash
cd /Users/<USER>/Documents/NIRA
python Scripts/systematic_content_generator.py
```

### **3. Existing Audio Generation Scripts** ✅
- `Scripts/batch_audio_generation.py` - For generating audio files
- Uses ElevenLabs with approved voices (Freya/Elli)

---

## 🚦 **EXECUTION PLAN**

### **Phase 1: Create Lesson Structures**
```bash
python Scripts/systematic_multilevel_creator.py
```
This will create:
- A2: 30 lesson structures
- B1: 20 lesson structures
- B2: 20 lesson structures
- C1: 15 lesson structures
- C2: 15 lesson structures

### **Phase 2: Generate Content**
```bash
python Scripts/systematic_content_generator.py
```
This will populate all lessons with:
- Vocabulary (20 items each)
- Grammar (10 points each)
- Conversations (15 each)
- Exercises (15 each)

### **Phase 3: Generate Audio**
Use existing audio generation scripts for each level.

### **Phase 4: Validation**
Test all content in the NIRA app to ensure quality.

---

## 📊 **EXPECTED RESULTS**

### **Total Lessons Created**
- A2: 30 lessons × (20+10+15+15) = 1,800 content items
- B1: 20 lessons × (20+10+15+15) = 1,200 content items
- B2: 20 lessons × (20+10+15+15) = 1,200 content items
- C1: 15 lessons × (20+10+15+15) = 900 content items
- C2: 15 lessons × (20+10+15+15) = 900 content items
- **TOTAL**: 100 lessons, 6,000 content items

### **Audio Files**
- ~150 audio files per lesson
- **TOTAL**: ~15,000 audio files

This systematic approach ensures no script failures and maintains consistency across all levels.
